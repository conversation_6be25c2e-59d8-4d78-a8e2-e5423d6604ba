import express from "express"
import {
  createTimeLog,
  getTimeLogsByTask,
  getMyTimeLogs,
  updateTimeLog,
  deleteTimeLog,
  getTimeLogById,
  getTaskTimeSummary,
} from "../controllers/timeLogController.js"
import { verifyToken, employeeOnly } from "../middleware/auth.js"

const router = express.Router()

// Apply authentication middleware to all routes
router.use(verifyToken)
router.use(employeeOnly)

// Time log CRUD operations
router.post("/create", createTimeLog)
router.get("/my-logs", getMyTimeLogs)
router.get("/:timeLogId", getTimeLogById)
router.put("/:timeLogId", updateTimeLog)
router.delete("/:timeLogId", deleteTimeLog)

// Task-specific time log operations
router.get("/task/:taskId", getTimeLogsByTask)
router.get("/task/:taskId/summary", getTaskTimeSummary)

export default router
