import TimeLog from "../models/TimeLog.js"
import ProjectTask from "../models/ProjectTask.js"
import PersonalTask from "../models/PersonalTask.js"
import TaskType from "../models/TaskType.js"

// Create a new time log
export const createTimeLog = async (req, res) => {
  try {
    const {
      taskId,
      taskType,
      subtaskId,
      taskTypeCategory,
      timeSpentMinutes,
      remark,
      workDate,
    } = req.body

    // Validate required fields
    if (!taskId || !taskType || !taskTypeCategory || !timeSpentMinutes || !remark) {
      return res.status(400).json({
        success: false,
        message: "Task ID, task type, task type category, time spent, and remark are required",
      })
    }

    // Validate task type
    if (!["ProjectTask", "PersonalTask"].includes(taskType)) {
      return res.status(400).json({
        success: false,
        message: "Invalid task type. Must be ProjectTask or PersonalTask",
      })
    }

    // Validate time spent
    if (timeSpentMinutes < 1 || timeSpentMinutes > 1440) { // Max 24 hours
      return res.status(400).json({
        success: false,
        message: "Time spent must be between 1 and 1440 minutes (24 hours)",
      })
    }

    // Verify task exists and user owns it
    const TaskModel = taskType === "ProjectTask" ? ProjectTask : PersonalTask
    const task = await TaskModel.findById(taskId)

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only log time for tasks you created",
      })
    }

    // Verify subtask exists if provided
    if (subtaskId) {
      const subtask = task.subtasks.id(subtaskId)
      if (!subtask) {
        return res.status(404).json({
          success: false,
          message: "Subtask not found",
        })
      }
    }

    // Verify task type category exists and is active
    const taskTypeDoc = await TaskType.findById(taskTypeCategory)
    if (!taskTypeDoc || !taskTypeDoc.isActive) {
      return res.status(404).json({
        success: false,
        message: "Task type category not found or inactive",
      })
    }

    // Create time log
    const timeLog = new TimeLog({
      taskId,
      taskType,
      subtaskId: subtaskId || undefined,
      loggedBy: req.user.id,
      taskTypeCategory,
      timeSpentMinutes,
      remark: remark.trim(),
      workDate: workDate ? new Date(workDate) : new Date(),
    })

    await timeLog.save()
    await timeLog.populate([
      { path: "taskTypeCategory", select: "name description" },
      { path: "taskId", select: "name title" },
    ])

    res.status(201).json({
      success: true,
      message: "Time log created successfully",
      data: timeLog,
    })
  } catch (error) {
    console.error("Error creating time log:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get time logs for a specific task
export const getTimeLogsByTask = async (req, res) => {
  try {
    const { taskId } = req.params
    const { taskType } = req.query

    if (!taskType || !["ProjectTask", "PersonalTask"].includes(taskType)) {
      return res.status(400).json({
        success: false,
        message: "Valid task type is required (ProjectTask or PersonalTask)",
      })
    }

    // Verify task exists and user owns it
    const TaskModel = taskType === "ProjectTask" ? ProjectTask : PersonalTask
    const task = await TaskModel.findById(taskId)

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only view time logs for tasks you created",
      })
    }

    // Get time logs for the task
    const timeLogs = await TimeLog.find({
      taskId,
      taskType,
      isActive: true,
    })
      .populate("taskTypeCategory", "name description")
      .sort({ workDate: -1, loggedAt: -1 })

    // Calculate total time logged
    const totalTimeLogged = await TimeLog.getTotalTimeForTask(taskId, taskType)

    res.json({
      success: true,
      data: {
        timeLogs,
        totalTimeLogged,
        task: {
          id: task._id,
          name: task.name || task.title,
          estimatedHours: task.estimatedHours,
          estimatedMinutes: task.estimatedMinutes,
        },
      },
    })
  } catch (error) {
    console.error("Error fetching time logs:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get all time logs for the current employee
export const getMyTimeLogs = async (req, res) => {
  try {
    const { page = 1, limit = 20, startDate, endDate, taskType } = req.query

    // Build query
    const query = {
      loggedBy: req.user.id,
      isActive: true,
    }

    if (startDate && endDate) {
      query.workDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      }
    }

    if (taskType && ["ProjectTask", "PersonalTask"].includes(taskType)) {
      query.taskType = taskType
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Get time logs with pagination
    const timeLogs = await TimeLog.find(query)
      .populate("taskTypeCategory", "name description")
      .populate("taskId", "name title")
      .sort({ workDate: -1, loggedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    // Get total count for pagination
    const totalCount = await TimeLog.countDocuments(query)

    // Calculate total time for the period
    const totalTimeResult = await TimeLog.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalMinutes: { $sum: "$timeSpentMinutes" },
          logCount: { $sum: 1 },
        },
      },
    ])

    const totalTime = totalTimeResult.length > 0 ? totalTimeResult[0] : { totalMinutes: 0, logCount: 0 }

    res.json({
      success: true,
      data: {
        timeLogs,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalCount,
          limit: parseInt(limit),
        },
        summary: {
          totalMinutes: totalTime.totalMinutes,
          totalHours: (totalTime.totalMinutes / 60).toFixed(2),
          logCount: totalTime.logCount,
        },
      },
    })
  } catch (error) {
    console.error("Error fetching employee time logs:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Update a time log
export const updateTimeLog = async (req, res) => {
  try {
    const { timeLogId } = req.params
    const { taskTypeCategory, timeSpentMinutes, remark, workDate } = req.body

    const timeLog = await TimeLog.findById(timeLogId)

    if (!timeLog) {
      return res.status(404).json({
        success: false,
        message: "Time log not found",
      })
    }

    // Check ownership
    if (timeLog.loggedBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only update your own time logs",
      })
    }

    // Validate updates
    if (taskTypeCategory) {
      const taskTypeDoc = await TaskType.findById(taskTypeCategory)
      if (!taskTypeDoc || !taskTypeDoc.isActive) {
        return res.status(404).json({
          success: false,
          message: "Task type category not found or inactive",
        })
      }
      timeLog.taskTypeCategory = taskTypeCategory
    }

    if (timeSpentMinutes !== undefined) {
      if (timeSpentMinutes < 1 || timeSpentMinutes > 1440) {
        return res.status(400).json({
          success: false,
          message: "Time spent must be between 1 and 1440 minutes (24 hours)",
        })
      }
      timeLog.timeSpentMinutes = timeSpentMinutes
    }

    if (remark !== undefined) {
      if (!remark.trim()) {
        return res.status(400).json({
          success: false,
          message: "Remark is required",
        })
      }
      timeLog.remark = remark.trim()
    }

    if (workDate) {
      timeLog.workDate = new Date(workDate)
    }

    await timeLog.save()
    await timeLog.populate([
      { path: "taskTypeCategory", select: "name description" },
      { path: "taskId", select: "name title" },
    ])

    res.json({
      success: true,
      message: "Time log updated successfully",
      data: timeLog,
    })
  } catch (error) {
    console.error("Error updating time log:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Delete a time log (soft delete)
export const deleteTimeLog = async (req, res) => {
  try {
    const { timeLogId } = req.params

    const timeLog = await TimeLog.findById(timeLogId)

    if (!timeLog) {
      return res.status(404).json({
        success: false,
        message: "Time log not found",
      })
    }

    // Check ownership
    if (timeLog.loggedBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only delete your own time logs",
      })
    }

    // Soft delete
    timeLog.isActive = false
    await timeLog.save()

    res.json({
      success: true,
      message: "Time log deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting time log:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get time log by ID
export const getTimeLogById = async (req, res) => {
  try {
    const { timeLogId } = req.params

    const timeLog = await TimeLog.findById(timeLogId)
      .populate("taskTypeCategory", "name description")
      .populate("taskId", "name title")

    if (!timeLog || !timeLog.isActive) {
      return res.status(404).json({
        success: false,
        message: "Time log not found",
      })
    }

    // Check ownership
    if (timeLog.loggedBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only view your own time logs",
      })
    }

    res.json({
      success: true,
      data: timeLog,
    })
  } catch (error) {
    console.error("Error fetching time log:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get time summary for a task (total logged vs estimated)
export const getTaskTimeSummary = async (req, res) => {
  try {
    const { taskId } = req.params
    const { taskType } = req.query

    if (!taskType || !["ProjectTask", "PersonalTask"].includes(taskType)) {
      return res.status(400).json({
        success: false,
        message: "Valid task type is required (ProjectTask or PersonalTask)",
      })
    }

    // Verify task exists and user owns it
    const TaskModel = taskType === "ProjectTask" ? ProjectTask : PersonalTask
    const task = await TaskModel.findById(taskId)

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only view time summary for tasks you created",
      })
    }

    // Get total time logged for main task
    const mainTaskTime = await TimeLog.getTotalTimeForTask(taskId, taskType)

    // Get time logged for each subtask
    const subtaskTimes = []
    if (task.subtasks && task.subtasks.length > 0) {
      for (const subtask of task.subtasks) {
        const subtaskTime = await TimeLog.getTotalTimeForTask(taskId, taskType, subtask._id)
        subtaskTimes.push({
          subtaskId: subtask._id,
          subtaskName: subtask.name || subtask.title,
          estimatedMinutes: (subtask.estimatedHours || 0) * 60 + (subtask.estimatedMinutes || 0),
          loggedMinutes: subtaskTime.totalMinutes,
          logCount: subtaskTime.logCount,
        })
      }
    }

    // Calculate totals
    const estimatedMinutes = (task.estimatedHours || 0) * 60 + (task.estimatedMinutes || 0)
    const totalLoggedMinutes = mainTaskTime.totalMinutes + subtaskTimes.reduce((sum, st) => sum + st.loggedMinutes, 0)
    const remainingMinutes = Math.max(0, estimatedMinutes - totalLoggedMinutes)
    const overageMinutes = totalLoggedMinutes > estimatedMinutes ? totalLoggedMinutes - estimatedMinutes : 0

    res.json({
      success: true,
      data: {
        task: {
          id: task._id,
          name: task.name || task.title,
          estimatedMinutes,
          estimatedFormatted: `${Math.floor(estimatedMinutes / 60)}h ${estimatedMinutes % 60}m`,
        },
        mainTask: {
          loggedMinutes: mainTaskTime.totalMinutes,
          logCount: mainTaskTime.logCount,
          loggedFormatted: `${Math.floor(mainTaskTime.totalMinutes / 60)}h ${mainTaskTime.totalMinutes % 60}m`,
        },
        subtasks: subtaskTimes,
        summary: {
          totalLoggedMinutes,
          totalLoggedFormatted: `${Math.floor(totalLoggedMinutes / 60)}h ${totalLoggedMinutes % 60}m`,
          remainingMinutes,
          remainingFormatted: `${Math.floor(remainingMinutes / 60)}h ${remainingMinutes % 60}m`,
          overageMinutes,
          overageFormatted: overageMinutes > 0 ? `${Math.floor(overageMinutes / 60)}h ${overageMinutes % 60}m` : "0h 0m",
          progressPercentage: estimatedMinutes > 0 ? Math.round((totalLoggedMinutes / estimatedMinutes) * 100) : 0,
        },
      },
    })
  } catch (error) {
    console.error("Error fetching task time summary:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}
