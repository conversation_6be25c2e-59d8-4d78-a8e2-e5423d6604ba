import { api } from "../lib/api"

export const timeLogService = {
  // Create a new time log
  createTimeLog: async (timeLogData) => {
    const response = await api.post("/timelog/create", timeLogData)
    return response.data
  },

  // Get time logs for a specific task
  getTimeLogsByTask: async (taskId, taskType) => {
    const response = await api.get(`/timelog/task/${taskId}?taskType=${taskType}`)
    return response.data
  },

  // Get my time logs with pagination and filters
  getMyTimeLogs: async (params = {}) => {
    const queryParams = new URLSearchParams()

    Object.keys(params).forEach((key) => {
      if (params[key] !== undefined && params[key] !== "") {
        queryParams.append(key, params[key])
      }
    })

    const response = await api.get(`/timelog/my-logs?${queryParams.toString()}`)
    return response.data
  },

  // Get time log by ID
  getTimeLogById: async (timeLogId) => {
    const response = await api.get(`/timelog/${timeLogId}`)
    return response.data
  },

  // Update time log
  updateTimeLog: async (timeLogId, updateData) => {
    const response = await api.put(`/timelog/${timeLogId}`, updateData)
    return response.data
  },

  // Delete time log
  deleteTimeLog: async (timeLogId) => {
    const response = await api.delete(`/timelog/${timeLogId}`)
    return response.data
  },

  // Get task time summary
  getTaskTimeSummary: async (taskId, taskType) => {
    const response = await api.get(`/timelog/task/${taskId}/summary?taskType=${taskType}`)
    return response.data
  },
}

export const taskTypeService = {
  // Get active task types for employees
  getActiveTaskTypes: async () => {
    const response = await api.get("/tasktype/active")
    return response.data
  },
}
