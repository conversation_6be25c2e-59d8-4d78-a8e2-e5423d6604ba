import mongoose from "mongoose"

const personalTaskSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    status: {
      type: String,
      enum: ["to do", "In Progress", "On Hold", "Completed", "Cancelled"],
      default: "to do",
    },
    estimatedHours: {
      type: Number,
      min: 0,
      default: 0,
    },
    estimatedMinutes: {
      type: Number,
      min: 0,
      max: 59,
      default: 0,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    dueDate: {
      type: Date,
    },
    startDate: {
      type: Date,
    },
    completedAt: {
      type: Date,
    },
    subtasks: [
      {
        title: {
          type: String,
          required: true,
          trim: true,
          maxlength: 200,
        },
        description: {
          type: String,
          trim: true,
          maxlength: 500,
        },
        status: {
          type: String,
          enum: ["to do", "In Progress", "On Hold", "Completed", "Cancelled"],
          default: "to do",
        },
        estimatedHours: {
          type: Number,
          min: 0,
          default: 0,
        },
        estimatedMinutes: {
          type: Number,
          min: 0,
          max: 59,
          default: 0,
        },
        completedAt: {
          type: Date,
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Index for better query performance
personalTaskSchema.index({ createdBy: 1, status: 1 })
personalTaskSchema.index({ createdBy: 1, createdAt: -1 })

// Virtual for completed subtasks count
personalTaskSchema.virtual("completedSubtasksCount").get(function () {
  return this.subtasks.filter((subtask) => subtask.status === "Completed").length
})

// Virtual for total estimated time in minutes
personalTaskSchema.virtual("totalEstimatedMinutes").get(function () {
  const taskTime = this.estimatedHours * 60 + this.estimatedMinutes
  const subtaskTime = this.subtasks.reduce((total, subtask) => {
    return total + (subtask.estimatedHours * 60 + subtask.estimatedMinutes)
  }, 0)
  return taskTime + subtaskTime
})

// Virtual for progress percentage
personalTaskSchema.virtual("progressPercentage").get(function () {
  if (this.status === "Completed") return 100
  if (this.status === "Cancelled") return 0
  if (this.subtasks.length === 0) {
    switch (this.status) {
      case "to do":
        return 0
      case "In Progress":
        return 50
      case "On Hold":
        return 25
      default:
        return 0
    }
  }

  const completedSubtasks = this.subtasks.filter((subtask) => subtask.status === "Completed").length
  return Math.round((completedSubtasks / this.subtasks.length) * 100)
})

// Method to get complete time tracking information
personalTaskSchema.methods.getTimeTrackingInfo = async function () {
  const TimeLog = mongoose.model("TimeLog")

  // Get total logged time for main task
  const mainTaskTime = await TimeLog.getTotalTimeForTask(this._id, "PersonalTask")

  // Get logged time for each subtask
  const subtaskTimes = []
  for (const subtask of this.subtasks) {
    const subtaskTime = await TimeLog.getTotalTimeForTask(this._id, "PersonalTask", subtask._id)
    subtaskTimes.push({
      subtaskId: subtask._id,
      subtaskName: subtask.title,
      estimatedMinutes: (subtask.estimatedHours || 0) * 60 + (subtask.estimatedMinutes || 0),
      loggedMinutes: subtaskTime.totalMinutes,
      logCount: subtaskTime.logCount,
    })
  }

  // Calculate totals
  const estimatedMinutes = this.totalEstimatedMinutes
  const totalLoggedMinutes = mainTaskTime.totalMinutes + subtaskTimes.reduce((sum, st) => sum + st.loggedMinutes, 0)
  const remainingMinutes = Math.max(0, estimatedMinutes - totalLoggedMinutes)
  const overageMinutes = totalLoggedMinutes > estimatedMinutes ? totalLoggedMinutes - estimatedMinutes : 0
  const progressPercentage = estimatedMinutes > 0 ? Math.round((totalLoggedMinutes / estimatedMinutes) * 100) : 0

  return {
    estimatedMinutes,
    totalLoggedMinutes,
    remainingMinutes,
    overageMinutes,
    progressPercentage,
    mainTaskLoggedMinutes: mainTaskTime.totalMinutes,
    mainTaskLogCount: mainTaskTime.logCount,
    subtaskTimes,
    isOverEstimate: totalLoggedMinutes > estimatedMinutes,
  }
}

// Method to check if task can have time logged
personalTaskSchema.methods.canLogTime = function () {
  return this.status !== "Cancelled" && this.isActive
}

// Method to mark task as completed
personalTaskSchema.methods.markCompleted = function () {
  this.status = "Completed"
  this.completedAt = new Date()
  return this.save()
}

// Method to add subtask
personalTaskSchema.methods.addSubtask = function (subtaskData) {
  this.subtasks.push(subtaskData)
  return this.save()
}

// Method to update subtask
personalTaskSchema.methods.updateSubtask = function (subtaskId, updateData) {
  const subtask = this.subtasks.id(subtaskId)
  if (!subtask) {
    throw new Error("Subtask not found")
  }

  Object.keys(updateData).forEach((key) => {
    subtask[key] = updateData[key]
  })

  if (updateData.status === "Completed" && !subtask.completedAt) {
    subtask.completedAt = new Date()
  }

  return this.save()
}

// Method to remove subtask
personalTaskSchema.methods.removeSubtask = function (subtaskId) {
  this.subtasks.pull(subtaskId)
  return this.save()
}

// Ensure virtuals are included in JSON output
personalTaskSchema.set("toJSON", { virtuals: true })
personalTaskSchema.set("toObject", { virtuals: true })

export default mongoose.model("PersonalTask", personalTaskSchema)
