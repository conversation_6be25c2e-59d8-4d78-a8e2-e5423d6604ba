import mongoose from "mongoose"

const timeLogSchema = new mongoose.Schema(
  {
    // Task reference - can be either ProjectTask or PersonalTask
    taskId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      refPath: "taskType",
    },
    taskType: {
      type: String,
      required: true,
      enum: ["ProjectTask", "PersonalTask"],
    },
    // Optional subtask reference (for tasks that have subtasks)
    subtaskId: {
      type: mongoose.Schema.Types.ObjectId,
      required: false,
    },
    // Employee who logged the time
    loggedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    // Task type for categorization (created by superadmin)
    taskTypeCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "TaskType",
      required: true,
    },
    // Time spent in minutes
    timeSpentMinutes: {
      type: Number,
      required: true,
      min: 1,
    },
    // Remark/description for the time log
    remark: {
      type: String,
      required: true,
      trim: true,
      maxlength: 1000,
    },
    // Date when the work was done (can be different from creation date)
    workDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    // Automatic tracking of when the log was created
    loggedAt: {
      type: Date,
      default: Date.now,
    },
    // Status for potential approval workflows
    status: {
      type: String,
      enum: ["pending", "approved", "rejected"],
      default: "approved", // Auto-approve for now
    },
    // Flag for active/inactive logs
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
)

// Indexes for better query performance
timeLogSchema.index({ taskId: 1, taskType: 1 })
timeLogSchema.index({ loggedBy: 1, workDate: -1 })
timeLogSchema.index({ taskTypeCategory: 1 })
timeLogSchema.index({ workDate: -1 })
timeLogSchema.index({ loggedBy: 1, taskId: 1, taskType: 1 })

// Virtual for time spent in hours and minutes format
timeLogSchema.virtual("timeSpentFormatted").get(function () {
  const hours = Math.floor(this.timeSpentMinutes / 60)
  const minutes = this.timeSpentMinutes % 60
  
  if (hours === 0) {
    return `${minutes}m`
  } else if (minutes === 0) {
    return `${hours}h`
  } else {
    return `${hours}h ${minutes}m`
  }
})

// Virtual for time spent in decimal hours
timeLogSchema.virtual("timeSpentHours").get(function () {
  return (this.timeSpentMinutes / 60).toFixed(2)
})

// Method to validate if user can log time for this task
timeLogSchema.methods.validateTaskOwnership = async function () {
  const TaskModel = mongoose.model(this.taskType)
  const task = await TaskModel.findById(this.taskId)
  
  if (!task) {
    throw new Error("Task not found")
  }
  
  // Check if the logged user is the task creator
  if (task.createdBy.toString() !== this.loggedBy.toString()) {
    throw new Error("You can only log time for tasks you created")
  }
  
  return task
}

// Static method to get total time logged for a task
timeLogSchema.statics.getTotalTimeForTask = async function (taskId, taskType, subtaskId = null) {
  const query = { taskId, taskType, isActive: true }
  if (subtaskId) {
    query.subtaskId = subtaskId
  }
  
  const result = await this.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalMinutes: { $sum: "$timeSpentMinutes" },
        logCount: { $sum: 1 },
      },
    },
  ])
  
  return result.length > 0 ? result[0] : { totalMinutes: 0, logCount: 0 }
}

// Static method to get time logs for a specific date range
timeLogSchema.statics.getTimeLogsForDateRange = async function (employeeId, startDate, endDate) {
  return this.find({
    loggedBy: employeeId,
    workDate: {
      $gte: startDate,
      $lte: endDate,
    },
    isActive: true,
  })
    .populate("taskTypeCategory", "name description")
    .populate("taskId")
    .sort({ workDate: -1, loggedAt: -1 })
}

// Ensure virtuals are included in JSON output
timeLogSchema.set("toJSON", { virtuals: true })
timeLogSchema.set("toObject", { virtuals: true })

const TimeLog = mongoose.model("TimeLog", timeLogSchema)
export default TimeLog
