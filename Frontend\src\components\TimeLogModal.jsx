"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "./ui/Button"
import { Input } from "./ui/Input"
import { Select } from "./ui/Select"
import { Card } from "./ui/Card"
import { timeLogService, taskTypeService } from "../services/timeLogService"
import { X, Clock, Calendar, Tag } from "lucide-react"

const TimeLogModal = ({ 
  isOpen, 
  onClose, 
  task, 
  taskType, // "ProjectTask" or "PersonalTask"
  onTimeLogCreated 
}) => {
  const [formData, setFormData] = useState({
    subtaskId: "",
    taskTypeCategory: "",
    timeSpentHours: "",
    timeSpentMinutes: "",
    remark: "",
    workDate: new Date().toISOString().split('T')[0], // Today's date
  })
  const [taskTypes, setTaskTypes] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    if (isOpen) {
      fetchTaskTypes()
      // Reset form when modal opens
      setFormData({
        subtaskId: "",
        taskTypeCategory: "",
        timeSpentHours: "",
        timeSpentMinutes: "",
        remark: "",
        workDate: new Date().toISOString().split('T')[0],
      })
      setError("")
    }
  }, [isOpen])

  const fetchTaskTypes = async () => {
    try {
      const response = await taskTypeService.getActiveTaskTypes()
      setTaskTypes(response.data || [])
    } catch (err) {
      console.error("Failed to fetch task types:", err)
      setError("Failed to load task types")
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Validation
    if (!formData.taskTypeCategory) {
      setError("Please select a task type")
      return
    }
    
    if (!formData.remark.trim()) {
      setError("Please enter a remark")
      return
    }

    const hours = parseInt(formData.timeSpentHours) || 0
    const minutes = parseInt(formData.timeSpentMinutes) || 0
    
    if (hours === 0 && minutes === 0) {
      setError("Please enter time spent (hours and/or minutes)")
      return
    }

    const totalMinutes = hours * 60 + minutes

    try {
      setLoading(true)
      setError("")

      const timeLogData = {
        taskId: task._id,
        taskType,
        subtaskId: formData.subtaskId || undefined,
        taskTypeCategory: formData.taskTypeCategory,
        timeSpentMinutes: totalMinutes,
        remark: formData.remark.trim(),
        workDate: formData.workDate,
      }

      const response = await timeLogService.createTimeLog(timeLogData)
      
      if (onTimeLogCreated) {
        onTimeLogCreated(response.data)
      }
      
      onClose()
    } catch (err) {
      setError(err.response?.data?.message || "Failed to create time log")
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  const subtaskOptions = task?.subtasks?.map(subtask => ({
    value: subtask._id,
    label: subtask.name || subtask.title
  })) || []

  const taskTypeOptions = taskTypes.map(type => ({
    value: type._id,
    label: type.name
  }))

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Log Time
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* Task Info */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <h3 className="font-medium text-gray-900">
              {task?.name || task?.title}
            </h3>
            <p className="text-sm text-gray-600">
              {taskType === "ProjectTask" ? "Project Task" : "Personal Task"}
            </p>
          </div>

          {/* Subtask Selection */}
          {subtaskOptions.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subtask (Optional)
              </label>
              <Select
                value={formData.subtaskId}
                onValueChange={(value) => handleInputChange("subtaskId", value)}
                placeholder="Select subtask (or leave empty for main task)"
              >
                <option value="">Main Task</option>
                {subtaskOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
          )}

          {/* Task Type Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Task Type <span className="text-red-500">*</span>
            </label>
            <Select
              value={formData.taskTypeCategory}
              onValueChange={(value) => handleInputChange("taskTypeCategory", value)}
              placeholder="Select task type"
              required
            >
              {taskTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          </div>

          {/* Time Spent */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Spent <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  type="number"
                  min="0"
                  max="23"
                  placeholder="Hours"
                  value={formData.timeSpentHours}
                  onChange={(e) => handleInputChange("timeSpentHours", e.target.value)}
                />
                <span className="text-xs text-gray-500 mt-1 block">Hours</span>
              </div>
              <div className="flex-1">
                <Input
                  type="number"
                  min="0"
                  max="59"
                  placeholder="Minutes"
                  value={formData.timeSpentMinutes}
                  onChange={(e) => handleInputChange("timeSpentMinutes", e.target.value)}
                />
                <span className="text-xs text-gray-500 mt-1 block">Minutes</span>
              </div>
            </div>
          </div>

          {/* Work Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Work Date <span className="text-red-500">*</span>
            </label>
            <Input
              type="date"
              value={formData.workDate}
              onChange={(e) => handleInputChange("workDate", e.target.value)}
              required
            />
          </div>

          {/* Remark */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Remark <span className="text-red-500">*</span>
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows="3"
              placeholder="Describe what you worked on..."
              value={formData.remark}
              onChange={(e) => handleInputChange("remark", e.target.value)}
              required
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={loading}
            >
              {loading ? "Logging..." : "Log Time"}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  )
}

export default TimeLogModal
