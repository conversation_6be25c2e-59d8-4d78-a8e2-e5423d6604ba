"use client"

import { useState, useEffect } from "react"
import { Card } from "./ui/Card"
import { Badge } from "./ui/Badge"
import { timeLogService } from "../services/timeLogService"
import { Clock, Target, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react"

const TimeLogSummary = ({ task, taskType, refreshTrigger }) => {
  const [summary, setSummary] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    if (task?._id) {
      fetchSummary()
    }
  }, [task?._id, taskType, refreshTrigger])

  const fetchSummary = async () => {
    try {
      setLoading(true)
      const response = await timeLogService.getTaskTimeSummary(task._id, taskType)
      setSummary(response.data)
    } catch (err) {
      setError(err.response?.data?.message || "Failed to fetch time summary")
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours === 0) {
      return `${mins}m`
    } else if (mins === 0) {
      return `${hours}h`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  const getProgressColor = (percentage) => {
    if (percentage >= 100) return "text-green-600"
    if (percentage >= 75) return "text-yellow-600"
    if (percentage >= 50) return "text-blue-600"
    return "text-gray-600"
  }

  const getProgressBgColor = (percentage) => {
    if (percentage >= 100) return "bg-green-100 border-green-200"
    if (percentage >= 75) return "bg-yellow-100 border-yellow-200"
    if (percentage >= 50) return "bg-blue-100 border-blue-200"
    return "bg-gray-100 border-gray-200"
  }

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-red-600 text-center">{error}</div>
      </Card>
    )
  }

  if (!summary) {
    return null
  }

  const { task: taskInfo, mainTask, subtasks, summary: summaryData } = summary

  return (
    <div className="space-y-4">
      {/* Main Summary Card */}
      <Card className={`p-6 ${getProgressBgColor(summaryData.progressPercentage)}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Target className="h-5 w-5" />
            Time Tracking Summary
          </h3>
          <Badge 
            variant={summaryData.progressPercentage >= 100 ? "success" : "secondary"}
            className="text-sm"
          >
            {summaryData.progressPercentage}% Complete
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Estimated Time */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-gray-600 mb-1">
              <Target className="h-4 w-4" />
              <span className="text-sm">Estimated</span>
            </div>
            <div className="text-xl font-semibold text-gray-900">
              {taskInfo.estimatedFormatted}
            </div>
          </div>

          {/* Logged Time */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-gray-600 mb-1">
              <Clock className="h-4 w-4" />
              <span className="text-sm">Logged</span>
            </div>
            <div className={`text-xl font-semibold ${getProgressColor(summaryData.progressPercentage)}`}>
              {summaryData.totalLoggedFormatted}
            </div>
          </div>

          {/* Remaining/Overage */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-gray-600 mb-1">
              {summaryData.overageMinutes > 0 ? (
                <>
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">Overage</span>
                </>
              ) : (
                <>
                  <TrendingUp className="h-4 w-4" />
                  <span className="text-sm">Remaining</span>
                </>
              )}
            </div>
            <div className={`text-xl font-semibold ${
              summaryData.overageMinutes > 0 ? 'text-red-600' : 'text-green-600'
            }`}>
              {summaryData.overageMinutes > 0 
                ? summaryData.overageFormatted 
                : summaryData.remainingFormatted
              }
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{summaryData.progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                summaryData.progressPercentage >= 100 
                  ? 'bg-green-500' 
                  : summaryData.progressPercentage >= 75 
                    ? 'bg-yellow-500' 
                    : 'bg-blue-500'
              }`}
              style={{ width: `${Math.min(summaryData.progressPercentage, 100)}%` }}
            ></div>
          </div>
        </div>
      </Card>

      {/* Main Task Breakdown */}
      <Card className="p-4">
        <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Main Task
        </h4>
        <div className="flex items-center justify-between">
          <span className="text-gray-700">{taskInfo.name}</span>
          <div className="text-right">
            <div className="font-medium">{mainTask.loggedFormatted}</div>
            <div className="text-sm text-gray-500">
              {mainTask.logCount} log{mainTask.logCount !== 1 ? 's' : ''}
            </div>
          </div>
        </div>
      </Card>

      {/* Subtasks Breakdown */}
      {subtasks && subtasks.length > 0 && (
        <Card className="p-4">
          <h4 className="font-medium text-gray-900 mb-3">Subtasks</h4>
          <div className="space-y-3">
            {subtasks.map((subtask) => (
              <div key={subtask.subtaskId} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{subtask.subtaskName}</div>
                  <div className="text-sm text-gray-500">
                    Estimated: {formatTime(subtask.estimatedMinutes)}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">
                    {formatTime(subtask.loggedMinutes)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {subtask.logCount} log{subtask.logCount !== 1 ? 's' : ''}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Alerts */}
      {summaryData.overageMinutes > 0 && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Time Budget Exceeded</span>
          </div>
          <p className="text-red-700 mt-1">
            This task has exceeded its estimated time by {summaryData.overageFormatted}.
          </p>
        </Card>
      )}

      {summaryData.progressPercentage === 100 && summaryData.overageMinutes === 0 && (
        <Card className="p-4 bg-green-50 border-green-200">
          <div className="flex items-center gap-2 text-green-800">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Perfect Timing!</span>
          </div>
          <p className="text-green-700 mt-1">
            You've completed this task exactly within the estimated time.
          </p>
        </Card>
      )}
    </div>
  )
}

export default TimeLogSummary
