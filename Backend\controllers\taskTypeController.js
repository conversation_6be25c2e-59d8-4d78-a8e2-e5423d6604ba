import TaskType from "../models/TaskType.js"

// Create a new task type
const createTaskType = async (req, res) => {
  try {
    const { name, description } = req.body

    // Validate required fields
    if (!name || name.trim() === "") {
      return res.status(400).json({
        success: false,
        message: "Task type name is required",
      })
    }

    // Check if task type with same name already exists
    const existingTaskType = await TaskType.findOne({
      name: name.trim(),
      isActive: true,
    })

    if (existingTaskType) {
      return res.status(400).json({
        success: false,
        message: "Task type with this name already exists",
      })
    }

    // Create new task type
    const taskType = new TaskType({
      name: name.trim(),
      description: description?.trim() || "",
      createdBy: req.user.id,
    })

    await taskType.save()

    // Populate the createdBy field for response
    await taskType.populate("createdBy", "name email")

    res.status(201).json({
      success: true,
      message: "Task type created successfully",
      data: taskType,
    })
  } catch (error) {
    console.error("Error creating task type:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get all task types
const getAllTaskTypes = async (req, res) => {
  try {
    const { search, status, page = 1, limit = 10 } = req.query

    // Build query
    const query = {}

    if (search) {
      query.$or = [{ name: { $regex: search, $options: "i" } }, { description: { $regex: search, $options: "i" } }]
    }

    if (status !== undefined) {
      query.isActive = status === "true"
    }

    // Calculate pagination
    const skip = (Number.parseInt(page) - 1) * Number.parseInt(limit)

    // Get task types with pagination
    const taskTypes = await TaskType.find(query)
      .populate("createdBy", "name email")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number.parseInt(limit))

    // Get total count for pagination
    const total = await TaskType.countDocuments(query)

    res.json({
      success: true,
      data: taskTypes,
      pagination: {
        current: Number.parseInt(page),
        pages: Math.ceil(total / Number.parseInt(limit)),
        total,
      },
    })
  } catch (error) {
    console.error("Error fetching task types:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get a single task type by ID
const getTaskTypeById = async (req, res) => {
  try {
    const { taskTypeId } = req.params

    const taskType = await TaskType.findById(taskTypeId).populate("createdBy", "name email")

    if (!taskType) {
      return res.status(404).json({
        success: false,
        message: "Task type not found",
      })
    }

    res.json({
      success: true,
      data: taskType,
    })
  } catch (error) {
    console.error("Error fetching task type:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Update a task type
const updateTaskType = async (req, res) => {
  try {
    const { taskTypeId } = req.params
    const { name, description } = req.body

    // Validate required fields
    if (!name || name.trim() === "") {
      return res.status(400).json({
        success: false,
        message: "Task type name is required",
      })
    }

    // Check if task type exists
    const taskType = await TaskType.findById(taskTypeId)
    if (!taskType) {
      return res.status(404).json({
        success: false,
        message: "Task type not found",
      })
    }

    // Check if another task type with same name exists (excluding current one)
    const existingTaskType = await TaskType.findOne({
      name: name.trim(),
      _id: { $ne: taskTypeId },
      isActive: true,
    })

    if (existingTaskType) {
      return res.status(400).json({
        success: false,
        message: "Task type with this name already exists",
      })
    }

    // Update task type
    taskType.name = name.trim()
    taskType.description = description?.trim() || ""
    taskType.updatedAt = Date.now()

    await taskType.save()
    await taskType.populate("createdBy", "name email")

    res.json({
      success: true,
      message: "Task type updated successfully",
      data: taskType,
    })
  } catch (error) {
    console.error("Error updating task type:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Toggle task type status (active/inactive)
const toggleTaskTypeStatus = async (req, res) => {
  try {
    const { taskTypeId } = req.params

    const taskType = await TaskType.findById(taskTypeId)
    if (!taskType) {
      return res.status(404).json({
        success: false,
        message: "Task type not found",
      })
    }

    // Toggle status
    taskType.isActive = !taskType.isActive
    taskType.updatedAt = Date.now()

    await taskType.save()
    await taskType.populate("createdBy", "name email")

    res.json({
      success: true,
      message: `Task type ${taskType.isActive ? "activated" : "deactivated"} successfully`,
      data: taskType,
    })
  } catch (error) {
    console.error("Error toggling task type status:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Delete a task type
const deleteTaskType = async (req, res) => {
  try {
    const { taskTypeId } = req.params

    const taskType = await TaskType.findById(taskTypeId)
    if (!taskType) {
      return res.status(404).json({
        success: false,
        message: "Task type not found",
      })
    }

    // Instead of hard delete, we'll soft delete by setting isActive to false
    // This preserves data integrity if tasks are already using this type
    taskType.isActive = false
    taskType.updatedAt = Date.now()
    await taskType.save()

    res.json({
      success: true,
      message: "Task type deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting task type:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

// Get active task types for employees (for time logging)
const getActiveTaskTypesForEmployees = async (req, res) => {
  try {
    // Get only active task types for employees to use in time logging
    const taskTypes = await TaskType.find({ isActive: true })
      .select("name description")
      .sort({ name: 1 })

    res.json({
      success: true,
      data: taskTypes,
      message: "Active task types retrieved successfully",
    })
  } catch (error) {
    console.error("Error fetching active task types for employees:", error)
    res.status(500).json({
      success: false,
      message: "Internal server error",
    })
  }
}

export {
  createTaskType,
  getAllTaskTypes,
  getTaskTypeById,
  updateTaskType,
  toggleTaskTypeStatus,
  deleteTaskType,
  getActiveTaskTypesForEmployees,
}
