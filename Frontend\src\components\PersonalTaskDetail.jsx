"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "./ui/Button"
import { Select } from "./ui/Select"
import { Card } from "./ui/Card"
import { Badge } from "./ui/Badge"
import { personalTaskService } from "../services/personalTaskService"
import TimeLogModal from "./TimeLogModal"
import TimeLogList from "./TimeLogList"
import TimeLogSummary from "./TimeLogSummary"
import { ArrowLeft, Edit, Clock, Calendar, CheckCircle, Plus } from "lucide-react"

const STATUS_OPTIONS = [
  { value: "to do", label: "To Do" },
  { value: "In Progress", label: "In Progress" },
  { value: "On Hold", label: "On Hold" },
  { value: "Completed", label: "Completed" },
  { value: "Cancelled", label: "Cancelled" },
]

const STATUS_COLORS = {
  "to do": "bg-gray-100 text-gray-800",
  "In Progress": "bg-blue-100 text-blue-800",
  "On Hold": "bg-yellow-100 text-yellow-800",
  Completed: "bg-green-100 text-green-800",
  Cancelled: "bg-red-100 text-red-800",
}

const PersonalTaskDetail = ({ task: initialTask, onBack, onEdit }) => {
  const [task, setTask] = useState(initialTask)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [showTimeLogModal, setShowTimeLogModal] = useState(false)
  const [activeTab, setActiveTab] = useState("details") // "details", "timelog", "summary"
  const [timeLogRefreshTrigger, setTimeLogRefreshTrigger] = useState(0)

  useEffect(() => {
    setTask(initialTask)
  }, [initialTask])

  const handleStatusChange = async (newStatus) => {
    try {
      setLoading(true)
      const response = await personalTaskService.updateTask(task._id, { status: newStatus })
      setTask(response.task)
    } catch (err) {
      setError(err.response?.data?.message || "Failed to update task status")
    } finally {
      setLoading(false)
    }
  }

  const handleSubtaskStatusChange = async (subtaskId, newStatus) => {
    try {
      setLoading(true)
      const response = await personalTaskService.updateSubtask(task._id, subtaskId, { status: newStatus })
      setTask(response.task)
    } catch (err) {
      setError(err.response?.data?.message || "Failed to update subtask status")
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (hours, minutes) => {
    if (hours === 0 && minutes === 0) return "No estimate"
    if (hours === 0) return `${minutes}m`
    if (minutes === 0) return `${hours}h`
    return `${hours}h ${minutes}m`
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getTotalEstimatedTime = () => {
    const taskMinutes = task.estimatedHours * 60 + task.estimatedMinutes
    const subtaskMinutes =
      task.subtasks?.reduce((total, subtask) => {
        return total + subtask.estimatedHours * 60 + subtask.estimatedMinutes
      }, 0) || 0

    const totalMinutes = taskMinutes + subtaskMinutes
    const hours = Math.floor(totalMinutes / 60)
    const minutes = totalMinutes % 60

    return formatTime(hours, minutes)
  }

  const handleTimeLogCreated = (newTimeLog) => {
    setTimeLogRefreshTrigger(prev => prev + 1)
    setShowTimeLogModal(false)
  }

  if (!task) {
    return (
      <div className="flex items-center justify-center py-12">
        <p className="text-gray-600">Task not found</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{task.title}</h1>
            <p className="text-gray-600">Personal Task Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setShowTimeLogModal(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Log Time
          </Button>
          <Button onClick={() => onEdit(task)} className="flex items-center gap-2">
            <Edit className="w-4 h-4" />
            Edit Task
          </Button>
        </div>
      </div>

      {/* Error Message */}
      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">{error}</div>}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("details")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "details"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Task Details
          </button>
          <button
            onClick={() => setActiveTab("timelog")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "timelog"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Time Logs
          </button>
          <button
            onClick={() => setActiveTab("summary")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "summary"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Time Summary
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "details" && (
        <>
          {/* Task Overview */}
          <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Task Information</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <Select
                  value={task.status}
                  onChange={(e) => handleStatusChange(e.target.value)}
                  options={STATUS_OPTIONS}
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-md min-h-[80px]">
                  {task.description || "No description provided"}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold mb-4">Time & Progress</h2>

            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-gray-700">Task Estimate:</span>
                <span className="font-medium">{formatTime(task.estimatedHours, task.estimatedMinutes)}</span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-gray-700">Total Estimate:</span>
                <span className="font-medium">{getTotalEstimatedTime()}</span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="text-gray-700">Created:</span>
                <span className="font-medium">{formatDate(task.createdAt)}</span>
              </div>

              {task.completedAt && (
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-gray-700">Completed:</span>
                  <span className="font-medium">{formatDate(task.completedAt)}</span>
                </div>
              )}

              {/* Progress Bar */}
              {task.subtasks && task.subtasks.length > 0 && (
                <div>
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-gray-700">Overall Progress</span>
                    <span className="font-medium">{task.progress || 0}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${task.progress || 0}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Subtasks */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Subtasks ({task.subtasks?.length || 0})</h2>
          {task.subtasks && task.subtasks.length > 0 && (
            <div className="text-sm text-gray-600">
              {task.subtasks.filter((st) => st.status === "Completed").length} of {task.subtasks.length} completed
            </div>
          )}
        </div>

        {!task.subtasks || task.subtasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <CheckCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>No subtasks for this task</p>
            <p className="text-sm">Edit the task to add subtasks</p>
          </div>
        ) : (
          <div className="space-y-4">
            {task.subtasks.map((subtask) => (
              <div key={subtask._id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium">{subtask.title}</h3>
                      <Badge className={STATUS_COLORS[subtask.status]}>{subtask.status}</Badge>
                    </div>

                    {subtask.description && <p className="text-gray-600 text-sm mb-2">{subtask.description}</p>}

                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatTime(subtask.estimatedHours, subtask.estimatedMinutes)}
                      </div>
                      {subtask.completedAt && (
                        <div className="flex items-center gap-1">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          Completed {formatDate(subtask.completedAt)}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="ml-4">
                    <Select
                      value={subtask.status}
                      onChange={(e) => handleSubtaskStatusChange(subtask._id, e.target.value)}
                      options={STATUS_OPTIONS}
                      disabled={loading}
                      size="sm"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
        </>
      )}

      {/* Time Logs Tab */}
      {activeTab === "timelog" && (
        <TimeLogList
          task={task}
          taskType="PersonalTask"
          refreshTrigger={timeLogRefreshTrigger}
        />
      )}

      {/* Time Summary Tab */}
      {activeTab === "summary" && (
        <TimeLogSummary
          task={task}
          taskType="PersonalTask"
          refreshTrigger={timeLogRefreshTrigger}
        />
      )}

      {/* Time Log Modal */}
      <TimeLogModal
        isOpen={showTimeLogModal}
        onClose={() => setShowTimeLogModal(false)}
        task={task}
        taskType="PersonalTask"
        onTimeLogCreated={handleTimeLogCreated}
      />
    </div>
  )
}

export default PersonalTaskDetail
