"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "./ui/Button"
import { Card } from "./ui/Card"
import { Badge } from "./ui/Badge"
import { timeLogService } from "../services/timeLogService"
import { Clock, Calendar, Tag, Edit, Trash2, MessageSquare } from "lucide-react"

const TimeLogList = ({ task, taskType, refreshTrigger }) => {
  const [timeLogs, setTimeLogs] = useState([])
  const [totalTimeLogged, setTotalTimeLogged] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    if (task?._id) {
      fetchTimeLogs()
    }
  }, [task?._id, taskType, refreshTrigger])

  const fetchTimeLogs = async () => {
    try {
      setLoading(true)
      const response = await timeLogService.getTimeLogsByTask(task._id, taskType)
      setTimeLogs(response.data.timeLogs || [])
      setTotalTimeLogged(response.data.totalTimeLogged || { totalMinutes: 0, logCount: 0 })
    } catch (err) {
      setError(err.response?.data?.message || "Failed to fetch time logs")
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteTimeLog = async (timeLogId) => {
    if (!window.confirm("Are you sure you want to delete this time log?")) {
      return
    }

    try {
      await timeLogService.deleteTimeLog(timeLogId)
      // Refresh the list
      fetchTimeLogs()
    } catch (err) {
      setError(err.response?.data?.message || "Failed to delete time log")
    }
  }

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours === 0) {
      return `${mins}m`
    } else if (mins === 0) {
      return `${hours}h`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getSubtaskName = (subtaskId) => {
    if (!subtaskId || !task?.subtasks) return "Main Task"
    const subtask = task.subtasks.find(st => st._id === subtaskId)
    return subtask ? (subtask.name || subtask.title) : "Unknown Subtask"
  }

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Summary Card */}
      {totalTimeLogged && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">Total Time Logged</span>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-blue-900">
                {formatTime(totalTimeLogged.totalMinutes)}
              </div>
              <div className="text-sm text-blue-700">
                {totalTimeLogged.logCount} log{totalTimeLogged.logCount !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </Card>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Time Logs List */}
      {timeLogs.length === 0 ? (
        <Card className="p-6 text-center">
          <Clock className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">No Time Logs Yet</h3>
          <p className="text-gray-600">Start logging time to track your progress on this task.</p>
        </Card>
      ) : (
        <div className="space-y-3">
          {timeLogs.map((timeLog) => (
            <Card key={timeLog._id} className="p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Header */}
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {getSubtaskName(timeLog.subtaskId)}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Tag className="h-3 w-3 mr-1" />
                      {timeLog.taskTypeCategory?.name || "Unknown Type"}
                    </Badge>
                  </div>

                  {/* Time and Date */}
                  <div className="flex items-center gap-4 mb-2">
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span className="font-medium text-gray-900">
                        {formatTime(timeLog.timeSpentMinutes)}
                      </span>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      {formatDate(timeLog.workDate)}
                    </div>
                  </div>

                  {/* Remark */}
                  <div className="flex items-start gap-1 text-sm">
                    <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                    <p className="text-gray-700">{timeLog.remark}</p>
                  </div>

                  {/* Logged timestamp */}
                  <div className="text-xs text-gray-500 mt-2">
                    Logged on {formatDate(timeLog.loggedAt)}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-1 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      // TODO: Implement edit functionality
                      console.log("Edit time log:", timeLog._id)
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteTimeLog(timeLog._id)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

export default TimeLogList
